<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_semantic_navigation</name>
  <version>0.1.0</version>
  <description>Robot semantic navigation system using ROS2 Navigation and LLM</description>
  
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>MIT</license>
  
  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_python</buildtool_depend>
  
  <!-- ROS2 Navigation dependencies -->
  <depend>rclcpp</depend>
  <depend>rclpy</depend>
  <depend>nav2_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>std_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  
  <!-- Navigation stack -->
  <depend>nav2_bringup</depend>
  <depend>nav2_common</depend>
  <depend>nav2_core</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>nav2_dwb_controller</depend>
  <depend>nav2_map_server</depend>
  <depend>nav2_planner</depend>
  <depend>nav2_recoveries</depend>
  <depend>nav2_simple_commander</depend>
  <depend>nav2_util</depend>
  
  <!-- Additional dependencies -->
  <depend>action_msgs</depend>
  <depend>lifecycle_msgs</depend>
  <depend>visualization_msgs</depend>
  
  <!-- Test dependencies -->
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_cmake_pytest</test_depend>
  
  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
