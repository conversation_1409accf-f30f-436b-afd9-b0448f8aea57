# Core dependencies
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
sqlite3

# LLM and AI dependencies
openai==1.3.0
langchain==0.0.350
transformers==4.35.0
torch==2.1.0

# Data processing
numpy==1.24.3
pandas==2.1.3
scipy==1.11.4

# Networking and HTTP
requests==2.31.0
httpx==0.25.2
websockets==12.0

# Configuration and environment
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# Logging and monitoring
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# ROS2 Python dependencies (install via rosdep)
# rclpy
# nav2_simple_commander
# geometry_msgs
# std_msgs
# sensor_msgs
