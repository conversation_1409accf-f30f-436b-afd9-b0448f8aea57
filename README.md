# 机器人语义导航系统 (Robot Semantic Navigation)

基于ROS2 Navigation和LLM的机器人语义导航MVP系统。

## 系统架构

本系统采用分层架构设计，包含以下主要组件：

### 1. 用户交互层
- **Web界面**: 提供图形化用户界面
- **命令行接口**: 支持文本命令输入
- **语音输入**: 支持语音指令识别

### 2. 语义理解层
- **LLM服务**: 核心语义理解引擎
- **自然语言处理**: 文本预处理和标准化
- **意图识别**: 识别用户导航意图

### 3. 语义地图层
- **语义地图管理器**: 管理语义标签和位置映射
- **兴趣点数据库**: 存储POI信息
- **位置-语义映射**: 维护坐标与语义的对应关系

### 4. 导航协调层
- **导航协调器**: 协调语义理解和底层导航
- **任务规划器**: 制定导航任务计划
- **目标转换器**: 将语义目标转换为坐标目标

### 5. ROS2 Navigation层
- **Nav2 Stack**: ROS2导航框架
- **SLAM**: 同步定位与地图构建
- **路径规划器**: 计算最优路径
- **控制器**: 执行运动控制

## 功能特性

- 🗣️ **自然语言导航**: 支持"去厨房"、"找到充电桩"等自然语言指令
- 🗺️ **语义地图**: 将物理位置与语义标签关联
- 🤖 **智能规划**: 基于LLM的智能任务规划
- 🔄 **实时反馈**: 提供导航状态和进度反馈
- 🎯 **多目标支持**: 支持复杂的多步骤导航任务

## 技术栈

- **ROS2**: Humble/Iron
- **Nav2**: ROS2导航框架
- **Python**: 主要开发语言
- **FastAPI**: Web服务框架
- **SQLite**: 语义地图数据库
- **OpenAI API**: LLM服务（可替换为其他LLM）

## 快速开始

### 环境要求

- Ubuntu 22.04 LTS
- ROS2 Humble/Iron
- Python 3.8+
- Nav2

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd robot-agent
```

2. 安装依赖
```bash
# 安装ROS2依赖
rosdep install --from-paths src --ignore-src -r -y

# 安装Python依赖
pip install -r requirements.txt
```

3. 构建项目
```bash
colcon build
source install/setup.bash
```

4. 启动系统
```bash
# 启动导航系统
ros2 launch semantic_navigation semantic_nav.launch.py

# 启动语义服务
ros2 run semantic_navigation semantic_service
```

## 使用示例

```python
# 通过命令行发送语义导航指令
ros2 service call /semantic_navigation/navigate semantic_navigation_msgs/srv/Navigate "{command: '去厨房拿水杯'}"

# 通过Web界面
curl -X POST http://localhost:8000/navigate \
  -H "Content-Type: application/json" \
  -d '{"command": "去客厅的沙发旁边"}'
```

## 项目结构

```
robot-agent/
├── src/
│   ├── semantic_navigation/          # 主要功能包
│   ├── semantic_map/                 # 语义地图管理
│   ├── llm_service/                  # LLM服务
│   └── web_interface/                # Web界面
├── config/                           # 配置文件
├── launch/                           # 启动文件
├── docs/                            # 文档
└── tests/                           # 测试文件
```

## 开发计划

- [x] 架构设计
- [ ] 基础项目结构
- [ ] 语义地图管理模块
- [ ] LLM语义理解服务
- [ ] 导航协调器
- [ ] 用户交互接口
- [ ] 集成测试

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
